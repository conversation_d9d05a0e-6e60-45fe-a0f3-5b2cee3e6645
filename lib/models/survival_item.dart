import 'package:flutter/material.dart';

/// 생존 가이드 아이템을 위한 데이터 모델
class SurvivalItem {
  final String title;
  final IconData icon;
  final List<SurvivalSubItem> subItems;

  const SurvivalItem({
    required this.title,
    required this.icon,
    required this.subItems,
  });
}

/// 생존 가이드 서브 아이템을 위한 데이터 모델
class SurvivalSubItem {
  final String title;
  final List<String> details;

  const SurvivalSubItem({required this.title, required this.details});
}
