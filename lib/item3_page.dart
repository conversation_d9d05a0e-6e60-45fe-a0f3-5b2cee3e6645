import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';

import 'models/survival_item.dart';
import 'widgets/survival_list_widget.dart';

class Item3Page extends StatelessWidget {
  const Item3Page({super.key});

  // 지진 후 복구 및 안전 확인 데이터 정의
  List<SurvivalItem> get _earthquakeRecoveryItems => [
    SurvivalItem(
      title: '안전 점검 및 응급처치',
      icon: Icons.health_and_safety,
      subItems: [
        SurvivalSubItem(
          title: '1. 부상자 확인 및 응급처치',
          details: [
            '의식 확인 및 기도 확보',
            '출혈 부위 지혈 처치',
            '골절 부위 고정',
            '119 신고 및 구조 요청',
          ],
        ),
        SurvivalSubItem(
          title: '2. 건물 안전성 점검',
          details: [
            '균열이나 기울어진 부분 확인',
            '가스 누출 냄새 확인',
            '전기 시설 점검',
            '위험 지역 출입 금지',
          ],
        ),
      ],
    ),
    SurvivalItem(
      title: '생활 복구 및 정보 수집',
      icon: Icons.restore,
      subItems: [
        SurvivalSubItem(
          title: '1. 정보 수집 및 소통',
          details: [
            '라디오나 휴대폰으로 재해 정보 확인',
            '가족 및 지인 안전 확인',
            '대피소 위치 및 구호물품 정보 파악',
            '여진 경보에 주의',
          ],
        ),
        SurvivalSubItem(
          title: '2. 생활 환경 정리',
          details: [
            '깨진 유리나 위험물 정리',
            '식수 및 식료품 확보',
            '임시 거주지 마련',
            '보험 및 피해 신고 준비',
          ],
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          FlutterI18n.translate(context, 'list.item3'),
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SurvivalListWidget(items: _earthquakeRecoveryItems),
    );
  }
}
