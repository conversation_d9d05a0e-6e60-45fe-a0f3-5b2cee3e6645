import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';

import 'models/survival_item.dart';
import 'widgets/survival_list_widget.dart';

class Item2Page extends StatelessWidget {
  const Item2Page({super.key});

  // 지진 발생 시 행동 요령 데이터 정의
  List<SurvivalItem> get _earthquakeActionItems => [
    SurvivalItem(
      title: '지진 발생 시 즉시 행동',
      icon: Icons.warning,
      subItems: [
        SurvivalSubItem(
          title: '1. 첫 30초 - 생명 보호',
          details: [
            '머리를 보호하고 튼튼한 책상 아래로 대피',
            '문을 열어 출구 확보',
            '가스레인지, 전열기구 즉시 끄기',
            '엘리베이터 사용 금지',
          ],
        ),
        SurvivalSubItem(
          title: '2. 진동이 멈춘 후',
          details: ['신발을 신고 유리 파편 주의', '가스 밸브 잠그기', '전기 차단기 내리기', '라디오로 상황 파악'],
        ),
      ],
    ),
    SurvivalItem(
      title: '대피 및 구조 요청',
      icon: Icons.exit_to_app,
      subItems: [
        SurvivalSubItem(
          title: '1. 안전한 대피',
          details: [
            '계단을 이용하여 천천히 대피',
            '넓은 공터나 공원으로 이동',
            '건물 외벽에서 멀리 떨어지기',
            '가족과 연락하여 안전 확인',
          ],
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          FlutterI18n.translate(context, 'list.item2'),
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SurvivalListWidget(items: _earthquakeActionItems),
    );
  }
}
