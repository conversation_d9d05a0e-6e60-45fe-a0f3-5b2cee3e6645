import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';

import 'models/survival_item.dart';
import 'widgets/survival_list_widget.dart';

class Item1Page extends StatelessWidget {
  const Item1Page({super.key});

  // 지진 대비 데이터 정의
  List<SurvivalItem> get _earthquakePreparationItems => [
    SurvivalItem(
      title: '집에서 대비 하는 법',
      icon: Icons.home,
      subItems: [
        SurvivalSubItem(
          title: '1. 집 안에서의 안전 확보',
          details: [
            '탁자 아래와 같이 집 안에서 대피할 수 있는 안전한 대피 공간을 미리 파악해 둡니다.',
            '유리창이나 넘어지기 쉬운 가구 주변 등 위험한 위치를 확인해 두고 지진 발생 시 가까이 가지 않도록 합니다.',
            '깨진 유리 등에 다치지 않도록 두꺼운 실내화를 준비해 둡니다.',
            '화재를 일으킬 수 있는 난로나 위험물은 주의하여 관리합니다.',
          ],
        ),
        SurvivalSubItem(
          title: '2. 집 안에서 떨어지기 쉬운 물건을 고정',
          details: [
            '가구나 가전제품이 흔들릴 때 넘어지지 않도록 고정해 둡니다.',
            '텔레비전, 꽃병 등 떨어질 수 있는 물건은 높은 곳에 두지 않도록 합니다.',
            '그릇장 안의 물건들이 쏟아지지 않도록 문을 고정해 둡니다.',
            '창문 등의 유리 부분은 필름을 붙여 유리가 파손되지 않도록 합니다.',
          ],
        ),
        SurvivalSubItem(
          title: '3. 집을 안전하게 관리',
          details: [
            '가스 및 전기를 미리 점검합니다.',
            '건물이나 담장은 수시로 점검하고, 위험한 부분은 안전하게 수리합니다.',
            '건물의 균열을 발견하면 전문가에게 문의하여 보수하고 보강합니다.',
          ],
        ),
        SurvivalSubItem(
          title: '4. 평상시 가족회의를 통하여 위급한 상황에 대비',
          details: [
            '가스 및 전기를 차단하는 방법을 알아 둡니다.',
            '머물고 있는 곳 주위의 넓은 공간 등 대피할 수 있는 장소를 알아 둡니다.',
            '비응급처치하는 방법을 반복적으로 훈련하여 익혀 둡니다.',
          ],
        ),
      ],
    ),
    SurvivalItem(
      title: '회사에서 대비 하는 법',
      icon: Icons.work,
      subItems: [
        SurvivalSubItem(
          title: '1. 사무실 안전 확보',
          details: [
            '책상 아래 대피 공간 확보',
            '비상구 위치 및 대피 경로 숙지',
            '엘리베이터 사용 금지, 계단 이용',
            '동료들과 비상 연락망 구축',
          ],
        ),
      ],
    ),
    SurvivalItem(
      title: '야외에서 대비 하는 법',
      icon: Icons.park,
      subItems: [
        SurvivalSubItem(
          title: '1. 야외 활동 시 주의사항',
          details: [
            '건물이나 전신주에서 멀리 떨어진 곳으로 대피',
            '산사태나 절벽 근처 피하기',
            '차량 운전 중이면 안전한 곳에 정차',
            '해안가에서는 쓰나미 위험 대비',
          ],
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          FlutterI18n.translate(context, 'list.item1'),
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SurvivalListWidget(items: _earthquakePreparationItems),
    );
  }
}
