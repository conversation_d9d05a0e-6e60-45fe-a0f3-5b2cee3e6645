import 'package:flutter/material.dart';

import '../models/survival_item.dart';

/// 재사용 가능한 생존 가이드 리스트 위젯
class SurvivalListWidget extends StatelessWidget {
  final List<SurvivalItem> items;
  final EdgeInsets? padding;
  final double? headerFontSize;
  final double? subTitleFontSize;
  final double? detailFontSize;
  final Color? dividerColor;

  const SurvivalListWidget({
    super.key,
    required this.items,
    this.padding,
    this.headerFontSize,
    this.subTitleFontSize,
    this.detailFontSize,
    this.dividerColor,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            items.map((item) => _buildSurvivalItem(context, item)).toList(),
      ),
    );
  }

  Widget _buildSurvivalItem(BuildContext context, SurvivalItem item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildItemHeader(item),
        const SizedBox(height: 10),
        Divider(color: dividerColor),
        const SizedBox(height: 10),
        ...item.subItems.map((subItem) => _buildSubItem(subItem)),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildItemHeader(SurvivalItem item) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(item.icon),
        const SizedBox(width: 8),
        Text(
          item.title,
          style: TextStyle(
            fontSize: headerFontSize ?? 20,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildSubItem(SurvivalSubItem subItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          subItem.title,
          style: TextStyle(
            fontSize: subTitleFontSize ?? 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 5),
        ...subItem.details.map((detail) => _buildDetailItem(detail)),
        const SizedBox(height: 15),
      ],
    );
  }

  Widget _buildDetailItem(String detail) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.arrow_forward_ios, size: 12),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              detail,
              style: TextStyle(fontSize: detailFontSize ?? 14),
            ),
          ),
        ],
      ),
    );
  }
}
