# SurvivalListWidget 사용 가이드

## 개요
`SurvivalListWidget`은 지진 생존 가이드 앱에서 재사용 가능한 리스트 형태의 정보를 표시하기 위한 위젯입니다.

## 구조
- `SurvivalItem`: 메인 카테고리 (제목, 아이콘, 서브 아이템들)
- `SurvivalSubItem`: 서브 카테고리 (제목, 세부 내용들)

## 사용 방법

### 1. 기본 사용법
```dart
import 'models/survival_item.dart';
import 'widgets/survival_list_widget.dart';

List<SurvivalItem> items = [
  SurvivalItem(
    title: '집에서 대비 하는 법',
    icon: Icons.home,
    subItems: [
      SurvivalSubItem(
        title: '1. 집 안에서의 안전 확보',
        details: [
          '탁자 아래와 같이 집 안에서 대피할 수 있는 안전한 공간 미리 파악',
          '가구나 가전제품이 넘어지지 않도록 고정',
        ],
      ),
    ],
  ),
];

// 위젯 사용
SurvivalListWidget(items: items)
```

### 2. 커스터마이징 옵션
```dart
SurvivalListWidget(
  items: items,
  padding: EdgeInsets.all(20.0),           // 패딩 조정
  headerFontSize: 22,                      // 헤더 폰트 크기
  subTitleFontSize: 18,                    // 서브타이틀 폰트 크기
  detailFontSize: 16,                      // 세부내용 폰트 크기
  dividerColor: Colors.grey,               // 구분선 색상
)
```

## 특징
- 재사용 가능한 구조
- 커스터마이징 가능한 스타일링
- 자동 스크롤 지원
- 일관된 UI/UX 제공

## 파일 구조
```
lib/
├── models/
│   └── survival_item.dart          # 데이터 모델
├── widgets/
│   └── survival_list_widget.dart   # 재사용 가능한 위젯
└── pages/
    ├── item1_page.dart            # 지진 대비 페이지
    ├── item2_page.dart            # 지진 발생 시 행동 페이지
    └── item3_page.dart            # 지진 후 복구 페이지
```
