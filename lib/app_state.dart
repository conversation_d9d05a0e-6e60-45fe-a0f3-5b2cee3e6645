import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppState extends ChangeNotifier {
  late String language = 'ko';
  late SharedPreferences prefs;

  Future<void> init() async {
    try {
      prefs = await SharedPreferences.getInstance();
      language = prefs.getString('language') ?? 'ko';
    } catch (e) {
      print('Error loading preferences: $e');
      language = 'ko';
    }
  }

  void setLanguage(String language) {
    prefs.setString('language', language);
    notifyListeners();
  }

  Locale get locale => Locale(language);
}
