import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:survival_guide_earthquake/app_state.dart';
import 'package:survival_guide_earthquake/item1_page.dart';
import 'package:survival_guide_earthquake/item2_page.dart';
import 'package:survival_guide_earthquake/item3_page.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppState().init();

  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late FlutterI18nDelegate flutterI18nDelegate;

  @override
  void initState() {
    flutterI18nDelegate = FlutterI18nDelegate(
      translationLoader: FileTranslationLoader(
        basePath: 'assets/i18n',
        forcedLocale: AppState().locale,
      ),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Survival Guide Earthquake',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Color.fromARGB(255, 205, 218, 168),
        ),
      ),
      home: MyHomePage(),
      localizationsDelegates: [flutterI18nDelegate],
      supportedLocales: [Locale('ko', ''), Locale('en', '')],
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(
          FlutterI18n.translate(context, 'app_title'),
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.language),
            onPressed: () {
              // Handle language change
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(5.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            SizedBox(height: 20),
            ListTile(
              title: Text(
                FlutterI18n.translate(context, 'list.item1'),
                style: TextStyle(fontSize: 20),
              ),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => Item1Page()),
                );
              },
              subtitle: Text(
                FlutterI18n.translate(context, 'list.item1_description'),
              ),
            ),
            ListTile(
              title: Text(
                FlutterI18n.translate(context, 'list.item2'),
                style: TextStyle(fontSize: 20),
              ),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => Item2Page()),
                );
              },
              subtitle: Text(
                FlutterI18n.translate(context, 'list.item2_description'),
              ),
            ),
            ListTile(
              title: Text(
                FlutterI18n.translate(context, 'list.item3'),
                style: TextStyle(fontSize: 20),
              ),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => Item3Page()),
                );
              },
              subtitle: Text(
                FlutterI18n.translate(context, 'list.item3_description'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
